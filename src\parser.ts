import { daysBeforeMonths, normalizeDate, orderDateComponents, getYearPosition } from './date.js';
import {
  regexSplitTime,
  convertTime12to24,
  normalizeAMPM,
  normalizeTime,
} from './time.js';
import { convertJsonUnicodeEscapes, analyzeUnicodeContent } from './unicode-reverter.js';

// --- TYPE DEFINITIONS ---
interface Attachment {
  fileName: string;
}

// The final, structured message object
export interface ParsedMessage {
  timestamp: Date | null;
  sender: string;
  text: string;
  isSystemMessage: boolean;
  attachment?: Attachment;
}

// A raw message line before full processing
interface RawMessage {
  system: boolean;
  msg: string;
}

// Options for the parser
interface ParseStringOptions {
  daysFirst?: boolean;
  parseAttachments?: boolean;
}

// Instagram JSON types
interface InstagramParticipant {
  name: string;
}

interface InstagramMessage {
  sender_name: string;
  timestamp_ms: number;
  content?: string;
  is_geoblocked_for_viewer: boolean;
  is_unsent_image_by_messenger_kid_parent: boolean;
}

interface InstagramJSON {
  participants: InstagramParticipant[];
  messages: InstagramMessage[];
}

// Format pattern for timestamp consistency checking
interface TimestampFormat {
  hasBrackets: boolean;
  dateSeparator: string;
  yearFirst: boolean;
  hasComma: boolean;
  has12HourTime: boolean;
}


// --- REGULAR EXPRESSIONS ---
const sharedRegex =
  /^(?:\u200E|\u200F)*\[?(\d{1,4}[-/.]\s?\d{1,4}[-/.]\s?\d{1,4})[,.]?\s\D*?(\d{1,2}[.:]\d{1,2}(?:[.:]\d{1,2})?)(?:\s([ap]\.?\s?m\.?))?\]?(?:\s-|:)?\s/;
const authorAndMessageRegex = /(.+?):\s([^]*)/;
const messageRegex = /([^]+)/;
const regexParser = new RegExp(
  sharedRegex.source + authorAndMessageRegex.source,
  'i',
);
const regexParserSystem = new RegExp(
  sharedRegex.source + messageRegex.source,
  'i',
);
const regexAttachment =
  /^(?:\u200E|\u200F)*(?:<.+: (.+)>|([\w-]+\.\w+)\s\(.+\))/i;

// --- SYSTEM MESSAGE DETECTION ---
const SYSTEM_EVENT_TEXTS = [
  // English
  "Messages and calls are end-to-end encrypted", "created group", "added", "left", "changed the subject",
  "changed this group's icon", "changed their phone number", "Waiting for this message", "<Media omitted>",
  "You're now an admin", "Missed voice call", "Missed video call", "Voice call", "Video call",
  // German
  "Nachrichten und Anrufe sind Ende-zu-Ende-verschlüsselt", "hat die Gruppe erstellt", "wurde hinzugefügt",
  "hat die Gruppe verlassen", "hat die Nummer zu", "geändert", "Sprachanruf", "Videoanruf",
  "Verpasster Sprachanruf", "Verpasster Videoanruf", "Bild weggelassen", "GIF weggelassen",
  "Video weggelassen", "Sticker weggelassen", "Dokument weggelassen",
  // Turkish
  "Mesajlar ve aramalar uçtan uca şifrelidir", "grubunu oluşturdu", "eklendi", "ayrıldı", "konuyu değiştirdi",
  "grubun simgesini değiştirdi", "telefon numarasını değiştirdi", "Bu mesaj bekleniyor", "<Medya dahil edilmedi>",
  "Artık yöneticisiniz", "Cevapsız sesli arama", "Cevapsız görüntülü arama", "Sesli arama", "Görüntülü arama",
  "Eklendiniz", "Grup kurucusu"
];

const SYSTEM_LIKE_PHRASES = [
  "sticker omitted", "image omitted", "video omitted", "gif omitted",
  "this message was deleted.", "document omitted", "<media omitted>",
  "çıkartma dahil edilmedi", "resim dahil edilmedi", "video dahil edilmedi", "gif dahil edilmedi",
  "bu mesaj silindi.", "doküman dahil edilmedi", "<medya dahil edilmedi>"
];

// --- FORMAT DETECTION FUNCTIONS ---
/**
 * Analyzes a single line to extract its timestamp format characteristics
 */
function analyzeLineFormat(line: string): TimestampFormat | null {
  // Check if line matches message patterns
  if (!regexParser.test(line) && !regexParserSystem.test(line)) {
    return null;
  }

  // Simple detection: check if it starts with bracket
  const hasBrackets = line.trim().startsWith('[');

  // Extract the timestamp part more reliably
  let timestampPart = '';
  if (hasBrackets) {
    const bracketMatch = line.match(/^\[([^\]]+)\]/);
    timestampPart = bracketMatch ? bracketMatch[1] : '';
  } else {
    const nonBracketMatch = line.match(/^(?:\u200E|\u200F)*([^-:]+?)(?:\s*[-:]\s*)/);
    timestampPart = nonBracketMatch ? nonBracketMatch[1] : '';
  }

  if (!timestampPart) return null;

  // Detect date separator and format
  const dateMatch = timestampPart.match(/(\d{1,4})([-/.])(\d{1,4})\2(\d{1,4})/);
  if (!dateMatch) return null;

  const [, first, separator] = dateMatch;
  const dateSeparator = separator;
  const yearFirst = parseInt(first) > 1900; // If first component > 1900, it's likely a year

  // Check for comma (indicates different format style)
  const hasComma = timestampPart.includes(',');

  // Check for 12-hour time format (AM/PM)
  const has12HourTime = /[ap]\.?\s?m\.?/i.test(timestampPart);

  return {
    hasBrackets,
    dateSeparator,
    yearFirst,
    hasComma,
    has12HourTime
  };
}

/**
 * Analyzes multiple lines using majority vote to detect the most common timestamp format
 */
function detectTimestampFormat(lines: string[]): TimestampFormat | null {
  // Analyze more lines for better detection (up to 30)
  const sampleLines = lines.slice(0, 30);

  // Analyze each line and collect format characteristics
  const formatCounts = new Map<string, { format: TimestampFormat; count: number }>();

  sampleLines.forEach(line => {
    const format = analyzeLineFormat(line);
    if (format) {
      // Create a unique key for this format combination
      const key = JSON.stringify({
        hasBrackets: format.hasBrackets,
        dateSeparator: format.dateSeparator,
        yearFirst: format.yearFirst,
        hasComma: format.hasComma,
        has12HourTime: format.has12HourTime
      });

      if (formatCounts.has(key)) {
        formatCounts.get(key)!.count++;
      } else {
        formatCounts.set(key, { format, count: 1 });
      }
    }
  });

  if (formatCounts.size === 0) return null;

  // Find the format with the highest count (majority vote)
  const mostCommonEntry = [...formatCounts.values()]
    .sort((a, b) => b.count - a.count)[0];

  return mostCommonEntry.format;
}

/**
 * Checks if a line matches the established timestamp format
 */
function matchesTimestampFormat(line: string, format: TimestampFormat | null): boolean {
  if (!format) return true; // If no format established, allow all

  // Simple but effective check: does the line start with the same bracket pattern?
  const lineStartsWithBracket = line.trim().startsWith('[');

  // If format expects brackets but line doesn't have them, or vice versa, it's not a match
  if (lineStartsWithBracket !== format.hasBrackets) return false;

  // Extract timestamp part for further checks
  let timestampPart = '';
  if (format.hasBrackets) {
    const bracketMatch = line.match(/^\[([^\]]+)\]/);
    timestampPart = bracketMatch ? bracketMatch[1] : '';
  } else {
    const nonBracketMatch = line.match(/^(?:\u200E|\u200F)*([^-:]+?)(?:\s*[-:]\s*)/);
    timestampPart = nonBracketMatch ? nonBracketMatch[1] : '';
  }

  if (!timestampPart) return false;

  // Check date separator
  if (!timestampPart.includes(format.dateSeparator)) return false;

  // Check comma presence (important for distinguishing formats)
  const hasComma = timestampPart.includes(',');
  if (hasComma !== format.hasComma) return false;

  // Check 12-hour time format
  const has12HourTime = /[ap]\.?\s?m\.?/i.test(timestampPart);
  if (has12HourTime !== format.has12HourTime) return false;

  return true;
}

/**
 * Takes an array of lines and detects the lines that are part of a previous
 * message (multiline messages) and merges them.
 *
 * It also labels messages without an author as system messages.
 */
function makeArrayOfMessages(lines: string[]): RawMessage[] {
  // First, detect the consistent timestamp format from the beginning of the chat
  const timestampFormat = detectTimestampFormat(lines);

  return lines.reduce((acc: RawMessage[], line) => {
    const matchesNormalMessage = regexParser.test(line);
    const matchesSystemMessage = regexParserSystem.test(line);

    // Check if this line matches the established timestamp format
    const matchesFormat = matchesTimestampFormat(line, timestampFormat);

    // If the line matches message format AND matches the established format, treat as new message
    if (matchesNormalMessage && matchesFormat) {
      acc.push({ system: false, msg: line });
    }
    // If it matches system format AND matches the established format, treat as system message
    else if (matchesSystemMessage && matchesFormat) {
      acc.push({ system: true, msg: line });
    }
    // Otherwise, it's part of the previous message (including text that looks like WhatsApp format)
    else if (acc.length > 0) {
      const prevMessage = acc[acc.length - 1];
      acc[acc.length - 1] = {
        ...prevMessage,
        msg: `${prevMessage.msg}\n${line}`,
      };
    }
    // If no previous message exists and this doesn't look like a real message, skip it
    else {
      // This handles edge case of malformed input starting with non-message content
    }

    return acc;
  }, []);
}

/**
 * Parses an array of raw messages into an array of structured objects.
 */
function parseMessages(
  messages: RawMessage[],
  options: ParseStringOptions = {},
): ParsedMessage[] {
  let { daysFirst } = options;
  const { parseAttachments } = options;

  const parsed = messages.map(obj => {
    const { system, msg } = obj;

    if (system) {
      const match = regexParserSystem.exec(msg);
      if (!match) return null;
      const [, dateStr, timeStr, ampm, message] = match;

      return {
        date: dateStr,
        time: timeStr,
        ampm: ampm || null,
        sender: 'System',
        message,
        isSystemMessage: true
      };
    }

    const match = regexParser.exec(msg);
    if (!match) return null;
    const [, dateStr, timeStr, ampm, sender, message] = match;

    // Check if this looks like a system message even though it has a sender format
    const normalizedMessage = message.trim().toLowerCase();
    const isSystemLike = SYSTEM_LIKE_PHRASES.some(p => normalizedMessage === p);

    return {
      date: dateStr,
      time: timeStr,
      ampm: ampm || null,
      sender,
      message,
      isSystemMessage: msg.includes('\u200E') || isSystemLike
    };
  }).filter((p): p is Exclude<typeof p, null> => p !== null);

  // Determine date format dynamically
  const numericDates = Array.from(
    new Set(parsed.map(({ date }) => date)),
    date => orderDateComponents(date).map(Number),
  );
  const yearPosition = getYearPosition(numericDates);

  if (typeof daysFirst !== 'boolean') {
    // Only use daysBeforeMonths for non-ISO formats (when year is not first)
    daysFirst = yearPosition === 0 ? false : daysBeforeMonths(numericDates);
  }

  return parsed.map(({ date, time, ampm, sender, message, isSystemMessage }) => {
    const splitDate = orderDateComponents(date);

    // Determine date format and extract components accordingly
    let dateComponents: { year: string; month: string; day: string };

    if (yearPosition === 0) {
      // ISO format: YYYY-MM-DD
      dateComponents = { year: splitDate[0], month: splitDate[1], day: splitDate[2] };
    } else {
      // Traditional format with year at the end: DD/MM/YYYY or MM/DD/YYYY
      dateComponents = daysFirst
        ? { year: splitDate[2], month: splitDate[1], day: splitDate[0] }
        : { year: splitDate[2], month: splitDate[0], day: splitDate[1] };
    }

    const [year, month, day] = normalizeDate(dateComponents.year, dateComponents.month, dateComponents.day);

    const [hours, minutes, seconds] = normalizeTime(
      ampm ? convertTime12to24(time, normalizeAMPM(ampm)) : time,
    ).split(regexSplitTime);

    // Clean up sender name: remove ~ and optional space when followed by a name, but keep ~ if it's alone
    let cleanedSender = sender;

    // If it starts with ~ followed by a name (with or without space), remove the ~ and space
    if (/^~\s*\S/.test(sender)) {
      cleanedSender = sender.replace(/^~\s*/, '');
    }
    // If it's just ~ or ~ with only spaces, keep it as ~
    else if (/^~\s*$/.test(sender)) {
      cleanedSender = '~';
    }

    const finalObject: ParsedMessage = {
      timestamp: new Date(+year, +month - 1, +day, +hours, +minutes, +seconds || 0),
      sender: cleanedSender,
      text: message,
      isSystemMessage,
    };

    if (parseAttachments) {
      const attachment = parseMessageAttachment(message);
      if (attachment) finalObject.attachment = attachment;
    }

    return finalObject;
  });
}

/**
 * Parses a message extracting the attachment if it's present.
 */
function parseMessageAttachment(message: string): Attachment | null {
  const attachmentMatch = message.match(regexAttachment);

  if (!attachmentMatch) return null;
  return {
    fileName: (attachmentMatch[1] || attachmentMatch[2]).trim(),
  };
}

// Instagram system message patterns - these should keep the original sender
const INSTAGRAM_KEEP_SENDER_PATTERNS = [
  "sent an attachment", "liked a message", "reacted to", "started a video chat",
  "missed a video chat", "started a call", "missed a call",
  "reacted 👍 to your message", "reacted ❤ to your message", "reacted 😂 to your message",
  "reacted 😮 to your message", "reacted 😢 to your message", "reacted 😡 to your message",
  "reacted 👎 to your message", "reacted 🔥 to your message", "reacted 💯 to your message",
  "reacted ð to your message", "reacted to your message"
];

// Instagram system message patterns - these should change sender to "System"
const INSTAGRAM_SYSTEM_PATTERNS = [
  "you sent an attachment", "you liked a message", "you reacted", "you missed a video chat",
  "unsent a message", "changed the group photo", "added", "left the conversation",
  "removed", "changed the group name",
  "you reacted 👍 to", "you reacted ❤ to", "you reacted 😂 to",
  "you reacted 😮 to", "you reacted 😢 to", "you reacted 😡 to",
  "you reacted 👎 to", "you reacted 🔥 to", "you reacted 💯 to"
];

/**
 * Checks if an Instagram message should be treated as a system message
 */
export function isInstagramSystemMessage(content: string): boolean {
  const normalizedContent = content.toLowerCase().trim();

  // First check if it should keep sender (attachment, like, call, audio by others)
  const shouldKeepSender = INSTAGRAM_KEEP_SENDER_PATTERNS.some(pattern =>
    normalizedContent.includes(pattern.toLowerCase())
  );

  if (shouldKeepSender) {
    return true; // It's a system message but should keep sender
  }

  // Check if it's a system message that should change sender to "System"
  const shouldChangeToSystem = INSTAGRAM_SYSTEM_PATTERNS.some(pattern =>
    normalizedContent.includes(pattern.toLowerCase())
  );

  if (shouldChangeToSystem) {
    return true;
  }

  // Special handling for reaction messages with Unicode emojis
  // Pattern: "reacted [emoji] to your message"
  if (normalizedContent.startsWith('reacted ') && normalizedContent.includes('to your message')) {
    return true;
  }

  // Handle Unicode-encoded emojis in reactions (like "ð")
  if (/reacted .+ to your message/.test(normalizedContent)) {
    return true;
  }

  return false;
}

/**
 * Checks if an Instagram message should change sender to "System"
 */
export function shouldChangeToSystemSender(content: string): boolean {
  const normalizedContent = content.toLowerCase().trim();
  return INSTAGRAM_SYSTEM_PATTERNS.some(pattern =>
    normalizedContent.includes(pattern.toLowerCase())
  );
}

/**
 * Parses Instagram JSON format and converts to ParsedMessage format
 * Automatically converts Unicode escape sequences to proper characters
 */
export function parseInstagramJSON(jsonContent: string): ParsedMessage[] {
  try {
    // First, analyze the Unicode content for logging
    const unicodeAnalysis = analyzeUnicodeContent(jsonContent);

    if (unicodeAnalysis.hasUnicode) {
      console.log(`🔄 Unicode conversion: Found ${unicodeAnalysis.totalEscapes} escape sequences (${unicodeAnalysis.uniqueEscapes} unique)`);

      // Convert Unicode escape sequences to proper characters
      jsonContent = convertJsonUnicodeEscapes(jsonContent);
      console.log('✅ Unicode conversion completed for Instagram JSON');
    }

    const data: InstagramJSON = JSON.parse(jsonContent);

    return data.messages
      .filter(msg => msg.content && msg.content.trim() !== '')
      .map(msg => {
        const content = msg.content || '';
        const isSystemMsg = isInstagramSystemMessage(content);
        const shouldChangeToSystem = shouldChangeToSystemSender(content);

        return {
          timestamp: new Date(msg.timestamp_ms),
          sender: shouldChangeToSystem ? 'System' : msg.sender_name,
          text: content,
          isSystemMessage: isSystemMsg,
        };
      })
      .sort((a, b) => (a.timestamp?.getTime() ?? 0) - (b.timestamp?.getTime() ?? 0));
  } catch (error) {
    throw new Error('Invalid Instagram JSON format');
  }
}

export function parseWhatsAppChat(
  chatContent: string,
  options: ParseStringOptions = {},
): ParsedMessage[] {
  const lines = chatContent.split('\n');
  const rawMessages = makeArrayOfMessages(lines);
  const parsedMessages = parseMessages(rawMessages, options);

  return parsedMessages.sort((a, b) => (a.timestamp?.getTime() ?? 0) - (b.timestamp?.getTime() ?? 0));
}

/**
 * Universal chat parser that auto-detects format and uses appropriate parser
 */
export function parseChatFile(
  fileContent: string,
  fileName: string,
  options: ParseStringOptions = {},
): ParsedMessage[] {
  // Detect file format based on content and filename
  const isJsonFile = fileName.toLowerCase().endsWith('.json');
  const looksLikeInstagramJson = fileContent.trim().startsWith('{') && 
                                 fileContent.includes('"participants"') && 
                                 fileContent.includes('"messages"');
  
  if (isJsonFile && looksLikeInstagramJson) {
    console.log("📱 Detected Instagram JSON format, using Instagram parser with Unicode conversion");
    return parseInstagramJSON(fileContent);
  } else if (isJsonFile) {
    console.log("📄 JSON file detected but doesn't look like Instagram format, trying Instagram parser with Unicode conversion anyway");
    return parseInstagramJSON(fileContent);
  } else {
    console.log("💬 Detected WhatsApp text format, using WhatsApp parser");
    return parseWhatsAppChat(fileContent, options);
  }
}
